{"name": "kufe-backend", "version": "1.0.0", "description": "Backend for Kandahar University Faculty of Economic", "main": "server.js", "scripts": {"start": "nodemon server.js'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["university", "management", "system", "education"], "author": "KUFE", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "hpp": "^0.2.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "path": "^0.12.7", "sharp": "^0.33.5", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.1"}}