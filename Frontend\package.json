{"name": "economic-faculty-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-tabs": "^1.1.4", "@tailwindcss/vite": "^4.1.4", "axios": "^1.8.4", "chart.js": "^4.4.0", "i": "^0.3.7", "lucide-react": "^0.487.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tailwindcss": "^4.1.4", "vite": "^6.2.0"}}